<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="SampleApp.MainPage">

    <ScrollView>
        <VerticalStackLayout
            Padding="30,0"
            Spacing="25">
            
            <Label
                Text="Maui Popups"
                Style="{StaticResource Headline}"
                SemanticProperties.HeadingLevel="Level1" 
                HorizontalOptions="Center" />

            <Label
                Text="Sample Application"
                Style="{StaticResource SubHeadline}"
                SemanticProperties.HeadingLevel="Level2"
                HorizontalOptions="Center"
                SemanticProperties.Description="Popup library demonstration" />

            <Label
                Text="This sample demonstrates the popup functionality"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Margin="0,10,0,20"
                TextColor="Gray" />

            <Button
                Text="Centered Popup XAML" 
                BackgroundColor="DarkBlue"
                TextColor="White"
                Clicked="OnSimplePopupClicked"
                HorizontalOptions="Fill" 
                Margin="0,5" />

            <Button
                Text="Fill Popup XAML" 
                BackgroundColor="SeaGreen"
                TextColor="White"
                Clicked="OnFillPopupClicked"
                HorizontalOptions="Fill" 
                Margin="0,5" />

            <Button
                x:Name="AnchoredButton"
                Text="Show Anchored Popup" 
                BackgroundColor="Orange"
                TextColor="White"
                Clicked="OnAnchoredPopupClicked"
                HorizontalOptions="Fill" 
                Margin="0,5" />

            <Button
                Text="Custom Popup Code"
                BackgroundColor="Purple"
                TextColor="White"
                Clicked="OnCustomSizePopupClicked"
                HorizontalOptions="Fill"
                Margin="0,5" />

            <Button
                Text="Navigation Stack Demo"
                BackgroundColor="DarkGreen"
                TextColor="White"
                Clicked="OnNavigationStackDemoClicked"
                HorizontalOptions="Fill"
                Margin="0,5" />

            <Button
                Text="iOS Gesture Test"
                BackgroundColor="Crimson"
                TextColor="White"
                Clicked="OnGestureTestClicked"
                HorizontalOptions="Fill"
                Margin="0,5" />


        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
