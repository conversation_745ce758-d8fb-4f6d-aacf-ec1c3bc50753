<?xml version="1.0" encoding="utf-8" ?>
<fastPopups:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                  xmlns:fastPopups="clr-namespace:FastPopups;assembly=FastPopups"
                  x:Class="SampleApp.Views.GestureTestPopup"
                  CloseWhenBackgroundIsClicked="True"
                  BackgroundColor="#80000000"
                  WidthRequest="300"
                  HeightRequest="400">
    
    <Border BackgroundColor="White"
            StrokeThickness="0"
            Stroke="Transparent">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="12"/>
        </Border.StrokeShape>
        
        <ScrollView>
            <StackLayout Padding="20" Spacing="15">
                
                <Label Text="iOS Gesture Test Popup"
                       FontSize="18"
                       FontAttributes="Bold"
                       HorizontalOptions="Center"/>
                
                <Label Text="This popup tests iOS gesture handling. Try the following:"
                       FontSize="14"
                       TextColor="Gray"/>
                
                <Label Text="✓ Tap buttons below (should work)"
                       FontSize="12"/>
                <Label Text="✓ Scroll this content (should work)"
                       FontSize="12"/>
                <Label Text="✓ Tap outside popup (should close)"
                       FontSize="12"/>
                
                <Button Text="Test Button 1"
                        BackgroundColor="LightBlue"
                        Clicked="OnButton1Clicked"/>
                
                <Button Text="Test Button 2"
                        BackgroundColor="LightGreen"
                        Clicked="OnButton2Clicked"/>
                
                <Entry Placeholder="Type here to test input"
                       x:Name="TestEntry"/>
                
                <Slider Minimum="0" Maximum="100" Value="50"
                        x:Name="TestSlider"/>
                
                <Label Text="Slider Value: 50"
                       x:Name="SliderValueLabel"/>
                
                <Switch x:Name="TestSwitch"/>
                
                <Label Text="Switch is OFF"
                       x:Name="SwitchLabel"/>
                
                <Label Text="Scroll down for more content..."
                       FontSize="12"
                       TextColor="Gray"
                       HorizontalOptions="Center"/>
                
                <!-- Add more content to test scrolling -->
                <BoxView HeightRequest="50" BackgroundColor="LightCoral"/>
                <BoxView HeightRequest="50" BackgroundColor="LightYellow"/>
                <BoxView HeightRequest="50" BackgroundColor="LightPink"/>
                <BoxView HeightRequest="50" BackgroundColor="LightCyan"/>
                
                <Label Text="End of scrollable content"
                       FontSize="12"
                       TextColor="Gray"
                       HorizontalOptions="Center"/>
                
                <Button Text="Close Popup"
                        BackgroundColor="Red"
                        TextColor="White"
                        Clicked="OnCloseClicked"/>
                
            </StackLayout>
        </ScrollView>
    </Border>
</fastPopups:Popup>
