<?xml version="1.0" encoding="utf-8"?>
<popup:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
              xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
              xmlns:popup="clr-namespace:FastPopups;assembly=FastPopups"
              x:Class="SampleApp.Views.SimplePopup"
              BackgroundColor="#66000000"
              Background="Transparent"
              HorizontalOptions="Center"
              VerticalOptions="Center"
              WidthRequest="300"
              CloseWhenBackgroundIsClicked="True">

    <Border BackgroundColor="GhostWhite"
            StrokeThickness="2"
            Stroke="DarkBlue"
            HeightRequest="-1"
            HorizontalOptions="Fill"
            VerticalOptions="Start"
            StrokeShape="RoundRectangle 10">

        <StackLayout Padding="20" Spacing="15" HorizontalOptions="Fill">
            <Label Text="Simple Popup"
                   FontSize="18"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   TextColor="DarkBlue" />

            <Label Text="This is a basic popup, will close only when button is clicked."
                   x:Name="LabelTest"
                   HorizontalOptions="Center"
                   HorizontalTextAlignment="Center"
                   TextColor="Gray" />

            <Button Text="Should Not Close"
                    BackgroundColor="DarkBlue"
                    TextColor="White"
                    Clicked="OnTestClicked" />

            <Button Text="Close"
                    BackgroundColor="DarkBlue"
                    TextColor="White"
                    Clicked="OnCloseClicked" />

        </StackLayout>

    </Border>

</popup:Popup>