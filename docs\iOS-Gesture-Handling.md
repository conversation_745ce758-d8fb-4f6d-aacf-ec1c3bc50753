# iOS Gesture Handling Solutions

## The Problem

On iOS, unlike Android and Windows, gestures are processed from back to front in the view hierarchy. This creates a specific issue with popup implementations:

1. **iOS**: Dimmer layer (overlay) receives gestures first → Content receives gestures second
2. **Android/Windows**: Content receives gestures first → Dimmer layer receives gestures second

When `CloseWhenBackgroundIsClicked = true`, this difference causes:
- **iOS**: Popup closes immediately without passing gestures to content
- **Android/Windows**: Content processes gestures first, popup only closes if content doesn't handle them

## Solutions Implemented

### Solution 1: Smart Hit Testing (Default)

The primary solution uses intelligent hit testing to determine whether a gesture should be handled by the dimmer layer or passed to content.

**How it works:**
- Custom `SmartDismissGestureRecognizer` performs hit testing on touch events
- If touch is within content area bounds, gesture is not handled (passed to content)
- If touch is outside content area (on dimmer), gesture triggers dismissal
- Includes gesture coordination to give content gesture recognizers priority

**Advantages:**
- Most accurate approach
- Maintains consistent behavior across platforms
- No artificial delays

### Solution 2: Delayed Dismissal (Fallback)

A fallback approach that introduces a small delay before dismissing the popup, allowing content gestures to be processed first.

**How it works:**
- 50ms delay before checking if popup should be dismissed
- Content gestures are processed during this delay
- If content handles the gesture, dismissal is prevented

**Advantages:**
- Simple and reliable
- Works in edge cases where hit testing might fail

**Disadvantages:**
- Introduces a small delay in dismissal
- Less precise than hit testing

## Usage

### Default Behavior (Recommended)

By default, the smart hit testing approach is used:

```csharp
var popup = new Popup
{
    Content = new MyPopupContent(),
    CloseWhenBackgroundIsClicked = true
};

await this.ShowPopupAsync(popup);
```

### Using Delayed Dismissal (Fallback)

If you encounter issues with the default approach, you can enable delayed dismissal:

```csharp
// Configure globally for all popups
PopupGestureConfiguration.ConfigureGestureHandling(useDelayedDismissal: true);

// Or check current configuration
bool isUsingDelayed = PopupGestureConfiguration.IsUsingDelayedDismissal();
```

### Platform-Specific Configuration

You can configure gesture handling specifically for iOS while leaving other platforms unchanged:

```csharp
#if IOS
PopupGestureConfiguration.ConfigureGestureHandling(useDelayedDismissal: true);
#endif
```

## Technical Details

### Smart Hit Testing Implementation

The `SmartDismissGestureRecognizer` class:

1. **ShouldReceiveTouch**: Performs hit testing to determine if touch is on content
2. **ShouldRequireFailureOf**: Coordinates with content gesture recognizers
3. **IsPointInContentArea**: Converts touch coordinates and tests bounds
4. **GetContentView**: Locates the actual content view within the popup hierarchy

### Gesture Coordination

The implementation includes several iOS-specific optimizations:

- `CancelsTouchesInView = false`: Allows touches to be delivered to other views
- `DelaysTouchesBegan = false`: Prevents artificial delays in touch delivery
- `DelaysTouchesEnded = false`: Ensures immediate touch completion
- Gesture recognizer priority management for content gestures

## Testing

To verify the solution works correctly:

1. Create a popup with interactive content (buttons, scrollviews, etc.)
2. Set `CloseWhenBackgroundIsClicked = true`
3. Test that:
   - Tapping content elements works normally
   - Tapping outside content area dismisses popup
   - Scrolling/swiping within content works
   - Gestures are not consumed inappropriately

## Migration

This solution is backward compatible. Existing code will automatically use the smart hit testing approach without any changes required.

If you experience any issues, you can easily switch to the delayed dismissal approach using the configuration API.
