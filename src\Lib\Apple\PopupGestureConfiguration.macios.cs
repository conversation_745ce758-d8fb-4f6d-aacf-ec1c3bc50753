#if MACCATALYST || IOS

namespace FastPopups;

/// <summary>
/// Configuration options for iOS popup gesture handling.
/// Provides solutions for the iOS-specific issue where gestures are processed from back to front,
/// causing the dimmer layer to consume gestures before content can handle them.
/// </summary>
public static class PopupGestureConfiguration
{
    /// <summary>
    /// Configures the popup gesture handling strategy for iOS.
    /// </summary>
    /// <param name="useDelayedDismissal">
    /// When true, uses a delayed dismissal approach that waits 50ms before dismissing the popup,
    /// allowing content gestures to be processed first. This is a fallback option.
    /// When false (default), uses intelligent hit testing to determine if gestures should be
    /// passed to content or handled for dismissal.
    /// </param>
    public static void ConfigureGestureHandling(bool useDelayedDismissal = false)
    {
        MauiPopup.UseDelayedDismissal = useDelayedDismissal;
    }

    /// <summary>
    /// Gets the current gesture handling strategy.
    /// </summary>
    /// <returns>True if using delayed dismissal, false if using hit testing approach.</returns>
    public static bool IsUsingDelayedDismissal()
    {
        return MauiPopup.UseDelayedDismissal;
    }
}

#endif
