using FastPopups;

namespace SampleApp.Views;

public partial class GestureTestPopup : Popup
{
    public GestureTestPopup()
    {
        InitializeComponent();
        
        // Subscribe to control events
        TestSlider.ValueChanged += OnSliderValueChanged;
        TestSwitch.Toggled += OnSwitchToggled;
    }

    private void OnButton1Clicked(object sender, EventArgs e)
    {
        DisplayAlert("Button 1", "Button 1 was clicked! Gestures are working correctly.", "OK");
    }

    private void OnButton2Clicked(object sender, EventArgs e)
    {
        DisplayAlert("Button 2", "Button 2 was clicked! Gestures are working correctly.", "OK");
    }

    private void OnSliderValueChanged(object sender, ValueChangedEventArgs e)
    {
        SliderValueLabel.Text = $"Slider Value: {e.NewValue:F0}";
    }

    private void OnSwitchToggled(object sender, ToggledEventArgs e)
    {
        SwitchLabel.Text = e.Value ? "Switch is ON" : "Switch is OFF";
    }

    private async void OnCloseClicked(object sender, EventArgs e)
    {
        await CloseAsync();
    }

    private async Task DisplayAlert(string title, string message, string cancel)
    {
        // Find the current page to display alert
        if (Application.Current?.MainPage != null)
        {
            await Application.Current.MainPage.DisplayAlert(title, message, cancel);
        }
    }
}
